using System.Collections;
using UnityEngine;

namespace Game.Objects
{
    /// <summary>
    /// Represents a character in the game with animation and dialogue capabilities.
    /// </summary>
    public class Character : BaseObject
    {
        [SerializeField] private AudioSource dialogueAudioSource;
        [SerializeField] private string voiceInstructions;
        [SerializeField] private string voiceActor;
        [SerializeField] private float voiceSpeed = 1.0f;

        private Coroutine talkCoroutine;

        public string talkAnimationName = "Talk";
        public string silentAnimationName = "Silent";
        public int talkAnimationCount = 3;
        public string talkingExpressionAnimationName = "TalkingExpression";
        public int talkingExpressionAnimationCount = 3;
        public float minTalkingExpressionCooldown = 1f;
        public float maxTalkingExpressionCooldown = 3f;

        /// <summary>
        /// AudioSource used for dialogue playback.
        /// </summary>
        public AudioSource DialogueAudioSource => dialogueAudioSource;

        /// <summary>
        /// voice instructions for this character.
        /// </summary>
        public string VoiceInstructions => voiceInstructions;

        /// <summary>
        /// name of the voice actor for this character.
        /// </summary>
        public string VoiceActor => voiceActor;

        /// <summary>
        /// Speed of the voice playback for this character.
        /// </summary>
        public float VoiceSpeed => voiceSpeed;

        public void PlayDialogue(AudioClip audio)
        {
            DialogueAudioSource.clip = audio;
            DialogueAudioSource.Play();

            if (talkCoroutine != null)
            {
                StopCoroutine(talkCoroutine);
            }
            talkCoroutine = StartCoroutine(TalkCoroutine(audio.length));
        }

        public void StopDialogue()
        {
            if (DialogueAudioSource.isPlaying)
            {
                DialogueAudioSource.Stop();

                if (talkCoroutine != null)
                {
                    StopCoroutine(talkCoroutine);
                    PlayAnimation(silentAnimationName);
                }
            }
        }

        private void Awake()
        {
            dialogueAudioSource.loop = false;
            dialogueAudioSource.playOnAwake = false;
        }

        private IEnumerator TalkCoroutine(float duration)
        {
            // Play initial talk animation
            int randomTalkId = Random.Range(0, talkAnimationCount);
            string finalTalkAnimation = $"{talkAnimationName}{randomTalkId}";
            PlayAnimation(finalTalkAnimation);

            // Play first talking expression animation immediately
            int randomTalkingExpressionId = Random.Range(0, talkingExpressionAnimationCount);
            string finalTalkingExpressionAnimation = $"{talkingExpressionAnimationName}{randomTalkingExpressionId}";
            PlayAnimation(finalTalkingExpressionAnimation);

            float elapsedTime = 0f;
            float lastExpressionTime = 0f;
            float nextExpressionTime = Random.Range(minTalkingExpressionCooldown, maxTalkingExpressionCooldown);

            // Continue playing talking expression animations during the dialogue
            while (elapsedTime < duration)
            {
                yield return null; // Wait one frame
                elapsedTime += Time.deltaTime;

                // Check if it's time for the next expression animation
                if (elapsedTime - lastExpressionTime >= nextExpressionTime)
                {
                    // Play another random talking expression animation
                    randomTalkingExpressionId = Random.Range(0, talkingExpressionAnimationCount);
                    finalTalkingExpressionAnimation = $"{talkingExpressionAnimationName}{randomTalkingExpressionId}";
                    PlayAnimation(finalTalkingExpressionAnimation);

                    lastExpressionTime = elapsedTime;
                    // Set random time for next expression animation
                    nextExpressionTime = Random.Range(minTalkingExpressionCooldown, maxTalkingExpressionCooldown);
                }
            }

            // Return to silent animation when dialogue ends
            PlayAnimation(silentAnimationName);
        }
    }
}