using System.Collections;
using UnityEngine;

namespace Game.Objects
{
    /// <summary>
    /// Represents a character in the game with animation and dialogue capabilities.
    /// </summary>
    public class Character : BaseObject
    {
        [SerializeField] private AudioSource dialogueAudioSource;
        [SerializeField] private string voiceInstructions;
        [SerializeField] private string voiceActor;
        [SerializeField] private float voiceSpeed = 1.0f;

        private Coroutine talkCoroutine;

        public const string TalkAnimationName = "Talk";
        public const string SilentAnimationName = "Silent";
        public const int TalkAnimationCount = 3;
        public const string TalkingExpressionAnimationName = "TalkingExpression";
        public const int TalkingExpressionAnimationCount = 3;
        public const float minTalkingExpressionCooldown = 1f;

        /// <summary>
        /// AudioSource used for dialogue playback.
        /// </summary>
        public AudioSource DialogueAudioSource => dialogueAudioSource;

        /// <summary>
        /// voice instructions for this character.
        /// </summary>
        public string VoiceInstructions => voiceInstructions;

        /// <summary>
        /// name of the voice actor for this character.
        /// </summary>
        public string VoiceActor => voiceActor;

        /// <summary>
        /// Speed of the voice playback for this character.
        /// </summary>
        public float VoiceSpeed => voiceSpeed;

        public void PlayDialogue(AudioClip audio)
        {
            DialogueAudioSource.clip = audio;
            DialogueAudioSource.Play();

            if (talkCoroutine != null)
            {
                StopCoroutine(talkCoroutine);
            }
            talkCoroutine = StartCoroutine(TalkCoroutine(audio.length));
        }

        public void StopDialogue()
        {
            if (DialogueAudioSource.isPlaying)
            {
                DialogueAudioSource.Stop();

                if (talkCoroutine != null)
                {
                    StopCoroutine(talkCoroutine);
                    PlayAnimation(SilentAnimationName);
                }
            }
        }

        private void Awake()
        {
            dialogueAudioSource.loop = false;
            dialogueAudioSource.playOnAwake = false;
        }

        private IEnumerator TalkCoroutine(float duration)
        {
            int randomTalkId = Random.Range(0, TalkAnimationCount);
            string finalTalkAnimation = $"{TalkAnimationName}{randomTalkId}";
            PlayAnimation(finalTalkAnimation);

            int randomTalkingExpressionId = Random.Range(0, TalkingExpressionAnimationCount);
            string finalTalkingExpressionAnimation = $"{TalkingExpressionAnimationName}{randomTalkingExpressionId}";
            PlayAnimation(finalTalkingExpressionAnimation);

            yield return new WaitForSeconds(duration);

            PlayAnimation(SilentAnimationName);
        }
    }
}