using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace Game.Objects
{
    public abstract class BaseObject : MonoBehaviour
    {
        public Animator Animator => animator;

        [SerializeField] protected Animator animator;

        // Cache for animation name hashes to avoid repeated string-to-hash conversions
        private readonly Dictionary<string, int> animationHashCache = new();

        // Cache for animation layer mappings to avoid repeated layer searches
        private readonly Dictionary<int, int> animationLayerCache = new();

        // Reusable WaitForSeconds instances to reduce garbage collection
        private readonly Dictionary<float, WaitForSeconds> waitForSecondsCache = new();

        /// <summary>
        /// Plays an animation with optional completion callback.
        /// Uses caching to optimize performance for repeated calls.
        /// </summary>
        /// <param name="animationName">Name of the animation to play</param>
        /// <param name="onAnimationComplete">Optional callback when animation completes</param>
        public void PlayAnimation(string animationName, UnityAction onAnimationComplete = null)
        {
            if (animator == null)
            {
                Debug.LogError("Animator component is not assigned.");
                return;
            }

            // Get cached hash or create and cache new one
            int animationHash = GetCachedAnimationHash(animationName);

            // Find the layer that contains this animation state (with caching)
            int targetLayer = GetCachedAnimationLayer(animationHash);
            if (targetLayer == -1)
            {
                Debug.LogError($"Animation '{animationName}' not found in any layer of the Animator Controller.");
                return;
            }

            animator.Play(animationHash, targetLayer);

            if (onAnimationComplete != null)
            {
                StartCoroutine(InvokeAfterAnimationDuration(onAnimationComplete, targetLayer));
            }
        }

        /// <summary>
        /// Gets cached animation hash or creates and caches a new one.
        /// </summary>
        private int GetCachedAnimationHash(string animationName)
        {
            if (!animationHashCache.TryGetValue(animationName, out int hash))
            {
                hash = Animator.StringToHash(animationName);
                animationHashCache[animationName] = hash;
            }
            return hash;
        }

        /// <summary>
        /// Gets cached animation layer or finds and caches it.
        /// </summary>
        private int GetCachedAnimationLayer(int animationHash)
        {
            if (!animationLayerCache.TryGetValue(animationHash, out int layer))
            {
                layer = FindAnimationLayer(animationHash);
                animationLayerCache[animationHash] = layer;
            }
            return layer;
        }

        /// <summary>
        /// Finds the layer containing the specified animation state.
        /// </summary>
        private int FindAnimationLayer(int animationHash)
        {
            if (animator.runtimeAnimatorController == null)
                return -1;

            // Check each layer for the animation state
            for (int layerIndex = 0; layerIndex < animator.layerCount; layerIndex++)
            {
                // Check if the animation state exists in this layer
                if (animator.HasState(layerIndex, animationHash))
                {
                    return layerIndex;
                }
            }

            return -1; // Animation not found in any layer
        }

        /// <summary>
        /// Gets a cached WaitForSeconds instance to reduce garbage collection.
        /// </summary>
        private WaitForSeconds GetCachedWaitForSeconds(float duration)
        {
            if (!waitForSecondsCache.TryGetValue(duration, out WaitForSeconds waitForSeconds))
            {
                waitForSeconds = new WaitForSeconds(duration);
                waitForSecondsCache[duration] = waitForSeconds;
            }
            return waitForSeconds;
        }

        private IEnumerator InvokeAfterAnimationDuration(UnityAction callback, int layerIndex)
        {
            // Wait one frame for the animation to start
            yield return null;

            // Get the current animation state info for the layer containing our animation
            AnimatorStateInfo stateInfo = animator.GetCurrentAnimatorStateInfo(layerIndex);

            // Calculate the actual duration based on clip length and speed
            float animationDuration = stateInfo.length / stateInfo.speed;

            // Use cached WaitForSeconds to reduce garbage collection
            yield return GetCachedWaitForSeconds(animationDuration);

            // Invoke the callback
            callback?.Invoke();
        }

        /// <summary>
        /// Clears all caches. Call this if the animator controller changes at runtime.
        /// </summary>
        protected virtual void ClearAnimationCaches()
        {
            animationHashCache.Clear();
            animationLayerCache.Clear();
            waitForSecondsCache.Clear();
        }

        protected virtual void OnDestroy()
        {
            ClearAnimationCaches();
        }
    }
}